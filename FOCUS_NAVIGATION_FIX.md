# 焦点导航修复说明

## 问题描述

在之前的实现中，当用户在视频列表中向右移动焦点后再向左移动时，焦点会直接跳转到左侧导航栏，而不是在视频列表内部正常移动。这是因为全局按键处理器会拦截所有的左键事件。

## 修复方案

### 1. 移除全局左键拦截

**修改文件：** `MainActivity.kt`

- 移除了全局按键处理器中对 `Key.DirectionLeft` 的拦截
- 保留了 `Key.Menu` 键的处理，用户仍可以通过Menu键快速返回左侧导航栏

```kotlin
// 修改前：拦截所有左键
keyEvent.key == Key.DirectionLeft && keyEvent.type == KeyEventType.KeyDown -> {
    sideNavigationFocusRequester.requestFocus()
    true
}

// 修改后：只保留Menu键
keyEvent.key == Key.Menu && keyEvent.type == KeyEventType.KeyDown -> {
    sideNavigationFocusRequester.requestFocus()
    true
}
```

### 2. 增强视频卡片的按键处理

**修改文件：** `TVVideoCard.kt`

- 添加了 `isFirstInRow` 参数，用于标识是否为行/列中的第一个卡片
- 添加了 `onNavigateToSidebar` 回调参数
- 实现了智能的左键处理逻辑：
  - 如果是第一个卡片且有侧边栏导航回调，则导航到侧边栏
  - 否则，使用正常的焦点移动逻辑

```kotlin
keyEvent.key == Key.DirectionLeft && keyEvent.type == KeyEventType.KeyDown -> {
    if (isFirstInRow && onNavigateToSidebar != null) {
        onNavigateToSidebar()
        true
    } else {
        focusManager.moveFocus(androidx.compose.ui.focus.FocusDirection.Left)
        true
    }
}
```

### 3. 更新组件传递参数

**修改文件：** `TVVideoRow.kt`, `HomeScreen.kt`, `CategoryScreen.kt`, `SearchScreen.kt`

- 在所有使用 `TVVideoCard` 的地方添加了 `isFirstInRow` 和 `onNavigateToSidebar` 参数
- 对于水平列表（LazyRow），第一个视频卡片的 `isFirstInRow = true`
- 对于网格布局（LazyVerticalGrid），第一列的视频卡片的 `isFirstInRow = true`

### 4. 建立回调链

**修改文件：** `MvNavigation.kt`, `MainActivity.kt`

- 从 `MainActivity` 开始，将侧边栏导航回调传递到各个页面
- 形成完整的回调链：`MainActivity` → `MvNavigation` → `各个Screen` → `TVVideoCard`

## 修复效果

### ✅ 修复前的问题
- 在视频列表中按左键会直接跳到左侧导航栏

### ✅ 修复后的行为
- 在视频列表中按左键会正常在视频之间移动焦点
- 只有在最左侧的视频卡片上按左键才会跳转到左侧导航栏
- 用户可以通过Menu键随时返回到左侧导航栏

## 适用场景

这个修复适用于以下布局：

1. **水平视频列表** (`TVVideoRow`)
   - 只有第一个视频卡片按左键会跳转到侧边栏
   - 其他卡片按左键会正常移动焦点

2. **视频网格** (`LazyVerticalGrid`)
   - 只有第一列的视频卡片按左键会跳转到侧边栏
   - 其他列的卡片按左键会正常移动焦点

## 遥控器操作

- **左右键**：在视频列表/网格内正常移动焦点
- **上下键**：在视频列表/网格内正常移动焦点
- **左键（在最左侧）**：跳转到左侧导航栏
- **Menu键**：随时返回到左侧导航栏
- **确认键/Enter键**：选择当前焦点的视频

## 构建状态

✅ 构建成功，无编译错误
✅ 逻辑清晰，用户体验得到改善
✅ 保持了原有的快速导航功能（Menu键） 