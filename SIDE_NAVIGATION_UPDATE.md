# 左侧导航更新说明

## 修改概述

根据需求，我们将原来的顶部导航栏移动到了左侧，并实现了焦点切换时自动导航的功能。

## 主要修改

### 1. 创建新的左侧导航组件 (`TVSideNavigation.kt`)

- 新建了 `TVSideNavigation` 组件，替代原来的 `TVTopNavigation`
- 导航栏位置：左侧垂直布局，宽度200dp
- 导航方式：上下键切换焦点，左右键与主内容区域交互
- 焦点自动导航：当焦点切换到某个导航项时，自动导航到对应页面

### 2. 更新导航目标定义 (`MvDestination.kt`)

- 为 `Category` 目标添加了 `categoryType` 参数
- 支持不同分类类型的路由：
  - `category/movies` - 电影
  - `category/tv` - 电视剧
  - `category/anime` - 动漫
  - `category/variety` - 综艺
  - `category/short` - 短剧
  - `category/documentary` - 纪录片

### 3. 更新主活动布局 (`MainActivity.kt`)

- 将布局从 `Column` 改为 `Row`，支持左侧导航栏
- 实现了焦点切换时自动导航的逻辑
- 更新了全局按键处理，左键和Menu键可以返回到左侧导航栏

### 4. 更新分类页面 (`CategoryScreen.kt` 和 `CategoryViewModel.kt`)

- 添加了 `categoryType` 参数支持
- 实现了根据分类类型自动选择对应分类的功能
- 当分类类型改变时，自动重新初始化数据

### 5. 更新导航逻辑 (`MvNavigation.kt`)

- 更新了分类页面的路由处理
- 修复了从首页跳转到分类页面的逻辑

## 导航映射

| 导航项 | 路由 | 分类ID |
|--------|------|--------|
| 搜索 | `search` | - |
| 首页 | `home` | - |
| 电影 | `category/movies` | 1 |
| 电视剧 | `category/tv` | 2 |
| 动漫 | `category/anime` | 4 |
| 综艺 | `category/variety` | 3 |
| 短剧 | `category/short` | 5 |
| 纪录片 | `category/documentary` | 6 |
| 我的 | `profile` | - |

## 遥控器操作

- **上下键**：在左侧导航栏中切换焦点，同时自动导航到对应页面
- **左右键**：在左侧导航栏和主内容区域之间切换焦点
- **确认键/Enter键**：选择当前焦点项
- **Menu键**：随时返回到左侧导航栏

## 特性

1. **焦点自动导航**：当遥控器焦点移动到某个导航项时，自动导航到对应页面，无需按确认键
2. **分类自动匹配**：不同的导航项会自动加载对应的分类内容
3. **焦点管理**：优化了焦点在导航栏和内容区域之间的切换
4. **状态保持**：导航时保持页面状态，提供更好的用户体验

## 构建状态

✅ 构建成功，无编译错误
✅ 修复了所有警告
✅ 代码结构清晰，易于维护 