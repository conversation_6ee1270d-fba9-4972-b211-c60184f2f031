# 详情页无法进入问题调试说明

## 问题描述

用户反馈无法进入详情页，点击视频卡片后没有反应。

## 调试方案

为了诊断这个问题，我们在关键位置添加了详细的调试日志，可以帮助定位问题所在。

## 添加的调试日志

### 1. TVVideoCard 点击事件日志
**文件**: `app/src/main/java/com/mv/app/ui/components/TVVideoCard.kt`

```kotlin
// 卡片点击事件
Card(
    onClick = { 
        Log.d("TVVideoCard", "卡片被点击，视频ID: ${video.id}, 标题: ${video.title}")
        onClick(video) 
    }
)

// 键盘事件
keyEvent.key == Key.Enter && keyEvent.type == KeyEventType.KeyDown -> {
    Log.d("TVVideoCard", "Enter键被按下，视频ID: ${video.id}")
    onClick(video)
    true
}
```

### 2. CategoryScreen 导航日志
**文件**: `app/src/main/java/com/mv/app/ui/category/CategoryScreen.kt`

```kotlin
onVideoClick = { video ->
    Log.d("CategoryScreen", "准备导航到详情页，视频ID: ${video.id}")
    val route = MvDestination.Detail.createRoute(video.id.toString())
    Log.d("CategoryScreen", "导航路由: $route")
    navController.navigate(route)
}
```

### 3. MvNavigation 路由日志
**文件**: `app/src/main/java/com/mv/app/ui/navigation/MvNavigation.kt`

```kotlin
composable(
    route = MvDestination.Detail.route,
    arguments = MvDestination.Detail.arguments
) { backStackEntry ->
    val videoId = backStackEntry.arguments?.getString("videoId") ?: ""
    Log.d("MvNavigation", "导航到详情页，videoId: $videoId")
    DetailScreen(navController = navController, videoId = videoId)
}
```

### 4. DetailScreen 创建日志
**文件**: `app/src/main/java/com/mv/app/ui/detail/DetailScreen.kt`

```kotlin
@Composable
fun DetailScreen(
    navController: NavController,
    videoId: String,
    viewModel: DetailViewModel = hiltViewModel()
) {
    Log.d("DetailScreen", "DetailScreen 被创建，videoId: $videoId")
    
    LaunchedEffect(videoId) {
        Log.d("DetailScreen", "开始加载视频详情，videoId: $videoId")
        viewModel.loadVideoDetail(videoId.toInt())
    }
}
```

### 5. DetailViewModel 数据加载日志
**文件**: `app/src/main/java/com/mv/app/ui/detail/DetailViewModel.kt`

```kotlin
fun loadVideoDetail(videoId: Int) {
    Log.d("DetailViewModel", "开始加载视频详情，videoId: $videoId")
    
    try {
        Log.d("DetailViewModel", "调用repository.getVideoDetail($videoId)")
        val result = repository.getVideoDetail(videoId)
        result.fold(
            onSuccess = { video ->
                Log.d("DetailViewModel", "成功获取视频详情，标题: ${video.title}")
            },
            onFailure = { exception ->
                Log.e("DetailViewModel", "获取视频详情失败: ${exception.message}")
            }
        )
    } catch (e: Exception) {
        Log.e("DetailViewModel", "加载视频详情异常: ${e.message}")
    }
}
```

### 6. CategoryViewModel 数据日志
**文件**: `app/src/main/java/com/mv/app/ui/category/CategoryViewModel.kt`

```kotlin
result.fold(
    onSuccess = { videos ->
        Log.d("CategoryViewModel", "成功获取视频数据，数量: ${videos.size}")
        videos.take(3).forEach { video ->
            Log.d("CategoryViewModel", "视频: ID=${video.id}, 标题=${video.title}")
        }
    }
)
```

## 调试流程

### 正常流程应该产生的日志
1. `CategoryViewModel`: "成功获取视频数据，数量: X"
2. `CategoryViewModel`: "视频: ID=X, 标题=XXX"
3. `TVVideoCard`: "卡片被点击，视频ID: X, 标题: XXX" 或 "Enter键被按下，视频ID: X"
4. `CategoryScreen`: "准备导航到详情页，视频ID: X"
5. `CategoryScreen`: "导航路由: detail/X"
6. `MvNavigation`: "导航到详情页，videoId: X"
7. `DetailScreen`: "DetailScreen 被创建，videoId: X"
8. `DetailScreen`: "开始加载视频详情，videoId: X"
9. `DetailViewModel`: "开始加载视频详情，videoId: X"
10. `DetailViewModel`: "调用repository.getVideoDetail(X)"
11. `DetailViewModel`: "成功获取视频详情，标题: XXX" 或错误日志

### 问题诊断

根据日志输出的中断位置，可以判断问题所在：

#### 情况1：没有视频数据
- 如果看不到 `CategoryViewModel` 的成功日志
- 问题：API没有返回数据或网络问题

#### 情况2：点击事件没有触发
- 如果看到视频数据日志，但看不到 `TVVideoCard` 的点击日志
- 问题：焦点管理或点击事件处理有问题

#### 情况3：导航没有执行
- 如果看到点击日志，但看不到 `CategoryScreen` 的导航日志
- 问题：导航回调没有正确传递

#### 情况4：路由解析失败
- 如果看到导航日志，但看不到 `MvNavigation` 的路由日志
- 问题：路由配置或导航控制器有问题

#### 情况5：DetailScreen没有创建
- 如果看到路由日志，但看不到 `DetailScreen` 的创建日志
- 问题：DetailScreen组件或Hilt注入有问题

#### 情况6：数据加载失败
- 如果看到DetailScreen创建日志，但看到 `DetailViewModel` 的错误日志
- 问题：API调用失败或数据解析问题

## 使用方法

### 1. 查看日志
在Android Studio中打开Logcat，使用以下过滤器：
- `TVVideoCard`
- `CategoryScreen`
- `MvNavigation`
- `DetailScreen`
- `DetailViewModel`
- `CategoryViewModel`

### 2. 测试步骤
1. 启动应用
2. 进入任意分类页面
3. 等待视频数据加载完成
4. 点击任意视频卡片
5. 观察日志输出

### 3. 分析结果
根据日志输出的中断位置，参考上述"问题诊断"部分确定问题所在。

## 可能的解决方案

### 如果是焦点问题
- 检查TVVideoCard的focusable属性
- 确认键盘事件处理没有阻止点击事件

### 如果是导航问题
- 检查NavController的状态
- 确认路由配置正确

### 如果是数据问题
- 检查API配置
- 确认网络连接
- 验证数据模型匹配

### 如果是ViewModel问题
- 检查Hilt依赖注入
- 确认Repository实现正确

## 编译状态

✅ **编译成功** - 所有调试日志已添加并通过编译测试
✅ **日志完整** - 覆盖了从点击到页面显示的完整流程
✅ **易于诊断** - 每个关键步骤都有对应的日志输出

现在可以通过运行应用并查看日志来准确定位详情页无法进入的具体原因。
