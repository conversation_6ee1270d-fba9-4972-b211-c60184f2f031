# Android TV播放器实现总结

## 功能概述

本次实现完成了Android TV视频应用的核心播放器功能，完全支持HLS播放和多线路/集数切换。

## 核心组件

### 1. PlayerViewModel
**文件**: `mv-app/app/src/main/java/com/mv/app/ui/player/PlayerViewModel.kt`

**功能**:
- 管理播放器状态和视频数据
- 处理线路和集数切换逻辑
- 支持API的playListByLine数据结构
- 提供播放控制方法（上一集/下一集）

**主要方法**:
- `loadVideoDetail(videoId)` - 加载视频详情和播放列表
- `selectLine(lineId)` - 选择播放线路
- `selectEpisode(episodeId)` - 选择集数
- `playNext()` / `playPrevious()` - 切换集数

### 2. VideoPlayer组件
**文件**: `mv-app/app/src/main/java/com/mv/app/ui/player/components/VideoPlayer.kt`

**功能**:
- 基于Media3 ExoPlayer的播放器封装
- 支持HLS流媒体播放
- 自动播放状态监听和错误处理
- Compose集成的AndroidView实现

**技术要点**:
- 使用`ExoPlayer.Builder`创建播放器实例
- 支持HLS格式（通过media3-exoplayer-hls依赖）
- 播放状态回调和错误处理

### 3. PlaylistSelector组件
**文件**: `mv-app/app/src/main/java/com/mv/app/ui/player/components/PlaylistSelector.kt`

**功能**:
- 线路和集数选择界面
- 支持水平滚动的筛选chip
- 提供紧凑模式的下拉选择器
- 实时更新选中状态

**两种模式**:
- `PlaylistSelector` - 完整展示模式
- `CompactPlaylistSelector` - 紧凑下拉模式

### 4. PlayerScreen主界面
**文件**: `mv-app/app/src/main/java/com/mv/app/ui/player/PlayerScreen.kt`

**功能**:
- 横竖屏自适应布局
- 横屏：全屏播放器 + 浮层控制
- 竖屏：16:9播放器 + 下方信息列表
- 完整的播放控制UI

**布局特点**:
- 响应式设计，自动检测屏幕方向
- 横屏时提供沉浸式播放体验
- 竖屏时提供完整信息展示

## 数据流程

### API数据结构支持
```json
{
  "playListByLine": {
    "1": [
      {
        "id": 1,
        "lineId": 1,
        "episode": 1,
        "url": "https://example.com/episode1.m3u8"
      }
    ],
    "2": [
      {
        "id": 2,
        "lineId": 2,
        "episode": 1,
        "url": "https://example2.com/episode1.m3u8"
      }
    ]
  }
}
```

### 播放流程
1. **数据加载**: PlayerViewModel从API加载视频详情
2. **线路选择**: 自动选择第一个可用线路
3. **集数选择**: 自动选择第一集或指定集数
4. **URL解析**: 提取对应的HLS播放链接
5. **播放器加载**: VideoPlayer组件加载并播放HLS流

## 用户交互

### 详情页操作
- 在DetailScreen显示完整的播放列表选择器
- 用户可选择线路和集数
- 点击播放按钮跳转到播放器

### 播放器操作
- **横屏模式**: 顶部标题栏 + 底部控制栏
- **竖屏模式**: 播放器 + 下方信息和控制
- 支持上一集/下一集切换
- 支持线路和集数重新选择

## 技术要点

### HLS支持
- 添加了`media3-exoplayer-hls`依赖
- 添加了`media3-datasource-okhttp`依赖
- ExoPlayer自动处理HLS格式解析

### 状态管理
- 使用StateFlow管理UI状态
- 响应式数据更新
- 自动状态同步

### 错误处理
- 播放错误自动捕获和显示
- 网络错误重试机制
- 友好的错误提示界面

## 集成说明

### 依赖配置
```kotlin
// Media3播放器依赖
implementation("androidx.media3:media3-exoplayer:1.2.1")
implementation("androidx.media3:media3-ui:1.2.1") 
implementation("androidx.media3:media3-common:1.2.1")
implementation("androidx.media3:media3-session:1.2.1")
implementation("androidx.media3:media3-exoplayer-hls:1.2.1")
implementation("androidx.media3:media3-datasource-okhttp:1.2.1")
```

### 权限要求
```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.WAKE_LOCK" />
```

## 使用示例

### 从详情页跳转播放器
```kotlin
// 在DetailScreen中
navController.navigate(
    MvDestination.Player.createRoute(videoId, episodeId)
)
```

### API数据映射
```kotlin
// PlayerViewModel中
val playListByLine = video.playListByLine ?: emptyMap()
val availableLines = playListByLine.keys.toList()
```

## 后续扩展

可以在现有基础上扩展的功能：
1. **播放历史记录** - 保存播放进度
2. **倍速播放** - 添加播放速度控制
3. **字幕支持** - 集成字幕显示
4. **断点续播** - 自动恢复播放位置
5. **画质切换** - 多清晰度支持

## 测试建议

1. **HLS播放测试** - 使用真实的m3u8链接测试
2. **网络异常测试** - 测试网络中断和恢复
3. **多集切换测试** - 验证集数切换逻辑
4. **横竖屏测试** - 验证布局适配
5. **多线路测试** - 验证线路切换功能

本实现为Android TV视频应用提供了完整的播放器解决方案，支持现代HLS流媒体播放需求。 