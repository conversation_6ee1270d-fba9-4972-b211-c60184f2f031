<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Base application theme for Android TV -->
    <style name="Theme.MvAndroidTV" parent="Theme.AppCompat.NoActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/tv_primary</item>
        <item name="colorPrimaryDark">@color/tv_primary_dark</item>
        <item name="colorAccent">@color/tv_accent</item>
        
        <!-- Background colors -->
        <item name="android:windowBackground">@color/tv_background</item>
        <item name="android:colorBackground">@color/tv_background</item>
        
        <!-- Text colors -->
        <item name="android:textColorPrimary">@color/tv_text_primary</item>
        <item name="android:textColorSecondary">@color/tv_text_secondary</item>
        
        <!-- Status bar color -->
        <item name="android:statusBarColor">@color/tv_background</item>
        <item name="android:navigationBarColor">@color/tv_background</item>
        
        <!-- Window flags -->
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        
        <!-- Enable hardware acceleration -->
        <item name="android:hardwareAccelerated">true</item>
    </style>
</resources> 