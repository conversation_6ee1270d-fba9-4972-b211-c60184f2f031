package com.mv.app.ui.detail

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.mv.app.data.model.Video
import com.mv.app.data.repository.VideoRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class DetailViewModel @Inject constructor(
    private val repository: VideoRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(DetailUiState())
    val uiState: StateFlow<DetailUiState> = _uiState.asStateFlow()

    fun loadVideoDetail(videoId: Int) {
        Log.d("DetailViewModel", "开始加载视频详情，videoId: $videoId")
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(
                isLoading = true,
                error = null
            )

            try {
                Log.d("DetailViewModel", "调用repository.getVideoDetail($videoId)")
                val result = repository.getVideoDetail(videoId)
                result.fold(
                    onSuccess = { video ->
                        Log.d("DetailViewModel", "成功获取视频详情，标题: ${video.title}")
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            video = video,
                            error = null
                        )
                    },
                    onFailure = { exception ->
                        Log.e("DetailViewModel", "获取视频详情失败: ${exception.message}")
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            error = exception.message ?: "加载视频详情失败"
                        )
                    }
                )
            } catch (e: Exception) {
                Log.e("DetailViewModel", "加载视频详情异常: ${e.message}")
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message ?: "加载视频详情失败"
                )
            }
        }
    }

    fun toggleFavorite() {
        val currentVideo = _uiState.value.video
        if (currentVideo != null) {
            _uiState.value = _uiState.value.copy(
                isFavorite = !_uiState.value.isFavorite
            )
            // TODO: 实现收藏功能的本地数据库操作
        }
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }

    fun refresh(videoId: Int) {
        loadVideoDetail(videoId)
    }
}

data class DetailUiState(
    val isLoading: Boolean = false,
    val video: Video? = null,
    val isFavorite: Boolean = false,
    val error: String? = null
) 