package com.mv.app.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.key.*
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlinx.coroutines.delay

@Composable
fun TVSideNavigation(
    selectedTabIndex: Int,
    onTabSelected: (Int) -> Unit,
    onTabFocused: (Int) -> Unit, // 焦点变化时的回调
    modifier: Modifier = Modifier
) {
    val tabs = listOf(
        "Search" to "搜索",
        "Home" to "首页",
        "Movies" to "电影",
        "TV" to "电视剧",
        "Anime" to "动漫",
        "Variety" to "综艺",
        "Short" to "短剧",
        "Documentary" to "纪录片",
        "Library" to "我的"
    )

    // 为每个标签页创建焦点请求器
    val focusRequesters = remember { List(tabs.size) { FocusRequester() } }
    val focusManager = LocalFocusManager.current
    var currentFocusIndex by remember { mutableIntStateOf(selectedTabIndex) }

    // 确保初始焦点
    LaunchedEffect(Unit) {
        delay(100)
        if (selectedTabIndex in focusRequesters.indices) {
            focusRequesters[selectedTabIndex].requestFocus()
            currentFocusIndex = selectedTabIndex
        }
    }

    // 当选中的标签页改变时，请求焦点
    LaunchedEffect(selectedTabIndex) {
        if (selectedTabIndex in focusRequesters.indices) {
            focusRequesters[selectedTabIndex].requestFocus()
            currentFocusIndex = selectedTabIndex
        }
    }
    
    Surface(
        modifier = modifier
            .fillMaxHeight()
            .width(120.dp)
            .background(
                color = MaterialTheme.colorScheme.surface.copy(alpha = 0.95f)
            )
            .padding(vertical = 24.dp, horizontal = 16.dp)
            .onFocusChanged { focusState ->
                // 当整个侧边栏获得焦点时，将焦点转移到当前选中的项
                if (focusState.isFocused && selectedTabIndex in focusRequesters.indices) {
                    focusRequesters[selectedTabIndex].requestFocus()
                }
            },
        color = MaterialTheme.colorScheme.surface.copy(alpha = 0.95f)
    ) {
        Column(
            verticalArrangement = Arrangement.spacedBy(12.dp),
            horizontalAlignment = Alignment.Start,
            modifier = Modifier
                .fillMaxHeight()
                .padding(vertical = 8.dp)
        ) {
            tabs.forEachIndexed { index, (_, chinese) ->
                TVSideNavigationTab(
                    title = chinese,
                    selected = selectedTabIndex == index,
                    onClick = { 
                        onTabSelected(index)
                        currentFocusIndex = index
                    },
                    onFocusChanged = { isFocused ->
                        if (isFocused) {
                            onTabFocused(index) // 焦点变化时触发导航
                            currentFocusIndex = index
                        }
                    },
                    focusRequester = focusRequesters[index],
                    onKeyEvent = { keyEvent ->
                        when {
                            keyEvent.key == Key.DirectionDown && keyEvent.type == KeyEventType.KeyDown -> {
                                val nextIndex = (index + 1) % tabs.size
                                focusRequesters[nextIndex].requestFocus()
                                true
                            }
                            keyEvent.key == Key.DirectionUp && keyEvent.type == KeyEventType.KeyDown -> {
                                val prevIndex = if (index == 0) tabs.size - 1 else index - 1
                                focusRequesters[prevIndex].requestFocus()
                                true
                            }
                            keyEvent.key == Key.DirectionRight && keyEvent.type == KeyEventType.KeyDown -> {
                                focusManager.moveFocus(androidx.compose.ui.focus.FocusDirection.Right)
                                true
                            }
                            keyEvent.key == Key.DirectionLeft && keyEvent.type == KeyEventType.KeyDown -> {
                                // 如果用户按左键，保持在导航栏
                                true
                            }
                            keyEvent.key == Key.Enter && keyEvent.type == KeyEventType.KeyDown -> {
                                onTabSelected(index)
                                true
                            }
                            keyEvent.key == Key.DirectionCenter && keyEvent.type == KeyEventType.KeyDown -> {
                                onTabSelected(index)
                                true
                            }
                            else -> false
                        }
                    },
                    modifier = Modifier.focusable()
                )
            }
        }
    }
}

@Composable
private fun TVSideNavigationTab(
    title: String,
    selected: Boolean,
    onClick: () -> Unit,
    onFocusChanged: (Boolean) -> Unit,
    focusRequester: FocusRequester,
    onKeyEvent: (KeyEvent) -> Boolean,
    modifier: Modifier = Modifier
) {
    var isFocused by remember { mutableStateOf(false) }
    
    Surface(
        onClick = onClick,
        modifier = modifier
            .fillMaxWidth()
            .focusRequester(focusRequester)
            .onFocusChanged { focusState ->
                isFocused = focusState.isFocused
                onFocusChanged(focusState.isFocused)
            }
            .onKeyEvent(onKeyEvent)
            .clip(RoundedCornerShape(8.dp))
            .padding(vertical = 2.dp),
        color = when {
            selected && isFocused -> MaterialTheme.colorScheme.primary.copy(alpha = 0.3f)
            selected -> MaterialTheme.colorScheme.primary.copy(alpha = 0.2f)
            isFocused -> MaterialTheme.colorScheme.surface.copy(alpha = 0.8f)
            else -> Color.Transparent
        },
        contentColor = when {
            selected -> MaterialTheme.colorScheme.primary
            isFocused -> MaterialTheme.colorScheme.onSurface
            else -> MaterialTheme.colorScheme.onSurfaceVariant
        },
        tonalElevation = if (isFocused) 4.dp else 0.dp
    ) {
        Text(
            text = title,
            fontSize = 16.sp,
            fontWeight = if (selected) FontWeight.Bold else FontWeight.Normal,
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 12.dp)
        )
    }
} 