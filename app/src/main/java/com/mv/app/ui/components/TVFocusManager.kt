package com.mv.app.ui.components

import androidx.compose.runtime.*
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.platform.LocalFocusManager
import kotlinx.coroutines.delay

/**
 * TV专用焦点管理器
 * 确保遥控器导航的稳定性和可靠性
 */
class TVFocusManager {
    companion object {
        /**
         * 安全地请求焦点，带有重试机制
         */
        suspend fun requestFocusSafely(
            focusRequester: FocusRequester,
            maxRetries: Int = 3,
            delayMs: Long = 100
        ): Bo<PERSON>an {
            repeat(maxRetries) { attempt ->
                try {
                    delay(delayMs * (attempt + 1))
                    focusRequester.requestFocus()
                    return true
                } catch (e: Exception) {
                    if (attempt == maxRetries - 1) {
                        // 最后一次尝试失败，记录错误但不抛出异常
                        println("Focus request failed after $maxRetries attempts: ${e.message}")
                    }
                }
            }
            return false
        }
        
        /**
         * 确保焦点在指定的FocusRequester上
         */
        suspend fun ensureFocus(
            focusRequester: FocusRequester,
            checkDelayMs: Long = 50,
            maxAttempts: Int = 5
        ) {
            repeat(maxAttempts) { attempt ->
                try {
                    delay(checkDelayMs)
                    focusRequester.requestFocus()
                    delay(checkDelayMs) // 给焦点时间生效
                    return
                } catch (e: Exception) {
                    if (attempt == maxAttempts - 1) {
                        println("Failed to ensure focus after $maxAttempts attempts: ${e.message}")
                    }
                }
            }
        }
    }
}

/**
 * 用于TV导航的Composable焦点管理器
 */
@Composable
fun rememberTVFocusManager(): TVFocusManager {
    return remember { TVFocusManager() }
}

/**
 * 确保初始焦点的Composable工具
 */
@Composable
fun EnsureInitialFocus(
    focusRequester: FocusRequester,
    enabled: Boolean = true,
    delayMs: Long = 200
) {
    LaunchedEffect(enabled) {
        if (enabled) {
            TVFocusManager.requestFocusSafely(
                focusRequester = focusRequester,
                delayMs = delayMs
            )
        }
    }
} 