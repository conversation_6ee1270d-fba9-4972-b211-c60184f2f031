package com.mv.app.ui.category

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.mv.app.data.model.Video
import com.mv.app.data.repository.VideoCategory
import com.mv.app.data.repository.VideoRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class CategoryViewModel @Inject constructor(
    private val repository: VideoRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(CategoryUiState())
    val uiState: StateFlow<CategoryUiState> = _uiState.asStateFlow()

    fun initializeWithCategoryType(categoryType: String) {
        Log.d("CategoryViewModel", "初始化分类类型: $categoryType")

        // 重置状态
        _uiState.value = CategoryUiState()

        loadCategories()
        // 根据分类类型选择对应的分类ID
        val typeId = when (categoryType) {
            "movies" -> 1      // 电影
            "tv" -> 2          // 电视剧
            "anime" -> 3       // 动漫
            "variety" -> 4     // 综艺
            "short" -> 7       // 短剧
            "documentary" -> 5 // 纪录片
            else -> 1 // 默认电影
        }

        Log.d("CategoryViewModel", "分类类型 $categoryType 对应的 typeId: $typeId")

        // 找到对应的分类并设置为选中状态
        val selectedCategory = repository.getCategories().find { it.id == typeId }
        _uiState.value = _uiState.value.copy(
            selectedCategory = selectedCategory,
            categories = repository.getCategories()
        )

        Log.d("CategoryViewModel", "选中的分类: ${selectedCategory?.name}")

        loadCategoryVideos(typeId)
    }

    private fun loadCategories() {
        _uiState.value = _uiState.value.copy(
            categories = repository.getCategories(),
            selectedCategory = repository.getCategories().firstOrNull()
        )
    }

    fun selectCategory(category: VideoCategory) {
        if (category.id != _uiState.value.selectedCategory?.id) {
            _uiState.value = _uiState.value.copy(
                selectedCategory = category,
                videos = emptyList(),
                currentPage = 0,
                hasMoreData = true
            )
            loadCategoryVideos(category.id)
        }
    }

    fun loadCategoryVideos(
        typeId: Int,
        loadMore: Boolean = false,
        area: String? = null,
        year: Int? = null
    ) {
        viewModelScope.launch {
            if (!loadMore) {
                _uiState.value = _uiState.value.copy(
                    isLoading = true,
                    error = null
                )
            } else {
                _uiState.value = _uiState.value.copy(isLoadingMore = true)
            }

            try {
                val currentPage = if (loadMore) _uiState.value.currentPage + 1 else 1
                val result = repository.getCategoryVideos(
                    typeId = typeId,
                    page = currentPage,
                    pageSize = 20,
                    area = area,
                    year = year
                )

                result.fold(
                    onSuccess = { videos ->
                        Log.d("CategoryViewModel", "成功获取视频数据，数量: ${videos.size}")
                        videos.take(3).forEach { video ->
                            Log.d("CategoryViewModel", "视频: ID=${video.id}, 标题=${video.title}")
                        }

                        val currentVideos = if (loadMore) _uiState.value.videos else emptyList()
                        val newVideos = currentVideos + videos

                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            isLoadingMore = false,
                            videos = newVideos,
                            currentPage = currentPage,
                            hasMoreData = videos.size == 20,
                            error = null
                        )
                    },
                    onFailure = { exception ->
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            isLoadingMore = false,
                            error = exception.message ?: "加载分类视频失败"
                        )
                    }
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    isLoadingMore = false,
                    error = e.message ?: "加载分类视频失败"
                )
            }
        }
    }

    fun loadMoreVideos() {
        val selectedCategory = _uiState.value.selectedCategory
        if (selectedCategory != null && _uiState.value.hasMoreData && !_uiState.value.isLoadingMore) {
            loadCategoryVideos(selectedCategory.id, loadMore = true)
        }
    }

    fun applyFilters(area: String? = null, year: Int? = null) {
        val selectedCategory = _uiState.value.selectedCategory
        if (selectedCategory != null) {
            _uiState.value = _uiState.value.copy(
                videos = emptyList(),
                currentPage = 0,
                hasMoreData = true,
                selectedArea = area,
                selectedYear = year
            )
            loadCategoryVideos(
                typeId = selectedCategory.id,
                area = area,
                year = year
            )
        }
    }

    fun clearFilters() {
        applyFilters(null, null)
    }

    fun refresh() {
        val selectedCategory = _uiState.value.selectedCategory
        if (selectedCategory != null) {
            _uiState.value = _uiState.value.copy(
                videos = emptyList(),
                currentPage = 0,
                hasMoreData = true
            )
            loadCategoryVideos(
                typeId = selectedCategory.id,
                area = _uiState.value.selectedArea,
                year = _uiState.value.selectedYear
            )
        }
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
}

data class CategoryUiState(
    val isLoading: Boolean = false,
    val isLoadingMore: Boolean = false,
    val categories: List<VideoCategory> = emptyList(),
    val selectedCategory: VideoCategory? = null,
    val videos: List<Video> = emptyList(),
    val currentPage: Int = 0,
    val hasMoreData: Boolean = true,
    val selectedArea: String? = null,
    val selectedYear: Int? = null,
    val error: String? = null
) 