package com.mv.app.ui.theme

import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color

// TV流媒体主题色彩
private val NetflixRed = Color(0xFFE50914)
private val DarkGray = Color(0xFF141414)
private val MediumGray = Color(0xFF1F1F1F)
private val LightGray = Color(0xFF2F2F2F)
private val TextWhite = Color(0xFFFFFFFF)
private val TextGray = Color(0xFFB3B3B3)
private val AccentBlue = Color(0xFF00D4FF)

// 深色主题 - 主要用于TV
private val DarkColorScheme = darkColorScheme(
    primary = AccentBlue,
    secondary = NetflixRed,
    tertiary = AccentBlue,
    background = DarkGray,
    surface = MediumGray,
    surfaceVariant = LightGray,
    onPrimary = Color.Black,
    onSecondary = Color.White,
    onTertiary = Color.Black,
    onBackground = TextWhite,
    onSurface = TextWhite,
    onSurfaceVariant = TextGray,
    primaryContainer = LightGray,
    onPrimaryContainer = TextWhite,
    secondaryContainer = NetflixRed.copy(alpha = 0.2f),
    onSecondaryContainer = TextWhite,
    outline = TextGray.copy(alpha = 0.5f),
    outlineVariant = TextGray.copy(alpha = 0.3f)
)

// 浅色主题 - 备用
private val LightColorScheme = lightColorScheme(
    primary = AccentBlue,
    secondary = NetflixRed,
    tertiary = AccentBlue,
    background = Color.White,
    surface = Color(0xFFF8F8F8),
    surfaceVariant = Color(0xFFF0F0F0),
    onPrimary = Color.White,
    onSecondary = Color.White,
    onTertiary = Color.White,
    onBackground = Color.Black,
    onSurface = Color.Black,
    onSurfaceVariant = Color(0xFF666666),
    primaryContainer = Color(0xFFE3F2FD),
    onPrimaryContainer = Color.Black,
    secondaryContainer = Color(0xFFFFEBEE),
    onSecondaryContainer = Color.Black
)

@Composable
fun MvAndroidTVTheme(
    darkTheme: Boolean = true, // TV应用默认使用深色主题
    content: @Composable () -> Unit
) {
    val colorScheme = if (darkTheme) {
        DarkColorScheme
    } else {
        LightColorScheme
    }

    MaterialTheme(
        colorScheme = colorScheme,
        typography = Typography,
        content = content
    )
} 