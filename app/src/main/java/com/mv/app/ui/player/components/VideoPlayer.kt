package com.mv.app.ui.player.components

import android.content.Context
import androidx.annotation.OptIn
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.viewinterop.AndroidView
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.ui.PlayerView

@OptIn(UnstableApi::class)
@Composable
fun VideoPlayer(
    url: String?,
    modifier: Modifier = Modifier,
    onPlaybackStateChanged: (isPlaying: Boolean, position: Long, duration: Long) -> Unit = { _, _, _ -> },
    onError: (String) -> Unit = {}
) {
    val context = LocalContext.current
    
    // 创建ExoPlayer实例
    val exoPlayer = remember {
        ExoPlayer.Builder(context)
            .build()
            .apply {
                // 监听播放状态变化
                addListener(object : Player.Listener {
                    override fun onPlaybackStateChanged(playbackState: Int) {
                        super.onPlaybackStateChanged(playbackState)
                        when (playbackState) {
                            Player.STATE_READY -> {
                                onPlaybackStateChanged(isPlaying, currentPosition, duration)
                            }
                            Player.STATE_ENDED -> {
                                onPlaybackStateChanged(false, currentPosition, duration)
                            }
                        }
                    }

                    override fun onIsPlayingChanged(isPlaying: Boolean) {
                        super.onIsPlayingChanged(isPlaying)
                        onPlaybackStateChanged(isPlaying, currentPosition, duration)
                    }

                    override fun onPlayerError(error: androidx.media3.common.PlaybackException) {
                        super.onPlayerError(error)
                        onError("播放错误: ${error.message}")
                    }
                })
            }
    }

    // 当URL变化时，更新播放源
    LaunchedEffect(url) {
        if (!url.isNullOrEmpty()) {
            try {
                val mediaItem = MediaItem.fromUri(url)
                exoPlayer.setMediaItem(mediaItem)
                exoPlayer.prepare()
                exoPlayer.play()
            } catch (e: Exception) {
                onError("加载视频失败: ${e.message}")
            }
        }
    }

    // 释放播放器
    DisposableEffect(Unit) {
        onDispose {
            exoPlayer.release()
        }
    }

    AndroidView(
        factory = { context ->
            PlayerView(context).apply {
                player = exoPlayer
                useController = true
                setShowBuffering(PlayerView.SHOW_BUFFERING_WHEN_PLAYING)
            }
        },
        modifier = modifier.fillMaxSize()
    )
} 