package com.mv.app.ui.category

import android.util.Log
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.GridItemSpan
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.grid.rememberLazyGridState
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.key.*
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavController
import com.mv.app.ui.components.TVVideoCard
import com.mv.app.ui.navigation.MvDestination
import kotlinx.coroutines.flow.collect

@Composable
fun CategoryScreen(
    navController: NavController,
    categoryType: String = "movies",
    onNavigateToSidebar: (() -> Unit)? = null,
    modifier: Modifier = Modifier,
    viewModel: CategoryViewModel = hiltViewModel(key = categoryType)
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    
    // 当分类类型改变时重新初始化
    LaunchedEffect(categoryType) {
        Log.d("CategoryScreen", "LaunchedEffect 触发，分类类型: $categoryType")
        viewModel.initializeWithCategoryType(categoryType)
    }

    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(horizontal = 48.dp, vertical = 24.dp)
    ) {
        // 分类标题
        CategoryHeader(categoryType = categoryType)

        when {
            uiState.isLoading -> {
                LoadingContent()
            }
            uiState.error != null -> {
                ErrorContent(
                    error = uiState.error!!,
                    onRetry = { viewModel.refresh() }
                )
            }
            else -> {
                SuccessContent(
                    uiState = uiState,
                    onVideoClick = { video ->
                        Log.d("CategoryScreen", "准备导航到详情页，视频ID: ${video.id}")
                        val route = MvDestination.Detail.createRoute(video.id.toString())
                        Log.d("CategoryScreen", "导航路由: $route")
                        navController.navigate(route)
                    },
                    onNavigateToSidebar = onNavigateToSidebar,
                    onLoadMore = { viewModel.loadMoreVideos() }
                )
            }
        }
    }
}

@Composable
private fun CategoryHeader(categoryType: String) {
    val categoryTitle = when (categoryType) {
        "movies" -> "电影"
        "tv" -> "电视剧"
        "anime" -> "动漫"
        "variety" -> "综艺"
        "short" -> "短剧"
        "documentary" -> "纪录片"
        else -> "分类"
    }

    Column(
        modifier = Modifier.padding(bottom = 24.dp)
    ) {
        Text(
            text = categoryTitle,
            style = MaterialTheme.typography.headlineLarge,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary,
            modifier = Modifier.padding(bottom = 8.dp)
        )

        Text(
            text = "当前分类类型: $categoryType",
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

@Composable
private fun LoadingContent() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        CircularProgressIndicator(
            color = MaterialTheme.colorScheme.primary
        )
    }
}

@Composable
private fun ErrorContent(
    error: String,
    onRetry: () -> Unit
) {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Text(
                text = error,
                style = MaterialTheme.typography.headlineSmall,
                color = MaterialTheme.colorScheme.error
            )
            
            Button(
                onClick = onRetry,
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.primary
                )
            ) {
                Text("重试")
            }
        }
    }
}

@Composable
private fun SuccessContent(
    uiState: CategoryUiState,
    onVideoClick: (com.mv.app.data.model.Video) -> Unit,
    onNavigateToSidebar: (() -> Unit)? = null,
    onLoadMore: () -> Unit
) {
    val gridState = rememberLazyGridState()

    // 检测是否需要加载更多内容
    LaunchedEffect(gridState) {
        snapshotFlow { gridState.layoutInfo.visibleItemsInfo }
            .collect { visibleItems ->
                if (visibleItems.isNotEmpty() && uiState.hasMoreData && !uiState.isLoadingMore) {
                    val totalItems = uiState.videos.size
                    val lastVisibleIndex = visibleItems.last().index
                    val columnsCount = 6
                    val totalRows = (totalItems + columnsCount - 1) / columnsCount
                    val lastVisibleRow = (lastVisibleIndex + columnsCount) / columnsCount

                    // 当滚动到倒数第二行时加载更多
                    if (lastVisibleRow >= totalRows - 1) {
                        Log.d("CategoryScreen", "触发加载更多，当前可见行: $lastVisibleRow, 总行数: $totalRows")
                        onLoadMore()
                    }
                }
            }
    }

    LazyVerticalGrid(
        state = gridState,
        columns = GridCells.Fixed(6),
        contentPadding = PaddingValues(vertical = 16.dp),
        horizontalArrangement = Arrangement.spacedBy(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp),
        modifier = Modifier.onKeyEvent { keyEvent ->
            when {
                keyEvent.key == Key.DirectionUp && keyEvent.type == KeyEventType.KeyDown -> {
                    if (gridState.firstVisibleItemIndex == 0) {
                        // 在顶部，不消费事件，让全局处理器处理
                        false
                    } else {
                        // 不在顶部，让Grid处理滚动
                        false
                    }
                }
                else -> false
            }
        }
    ) {
        items(uiState.videos) { video ->
            val isFirstInRow = uiState.videos.indexOf(video) % 6 == 0 // 6列网格，第一列
            TVVideoCard(
                video = video,
                onClick = onVideoClick,
                modifier = Modifier.fillMaxWidth(),
                isFirstInRow = isFirstInRow,
                onNavigateToSidebar = onNavigateToSidebar
            )
        }

        // 加载更多指示器
        if (uiState.isLoadingMore) {
            item(span = { GridItemSpan(6) }) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator(
                        color = MaterialTheme.colorScheme.primary
                    )
                }
            }
        }
    }
}