package com.mv.app.ui.player

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.ArrowForward
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.key.*
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavController
import com.mv.app.ui.player.components.VideoPlayer
import com.mv.app.ui.player.components.CompactPlaylistSelector

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PlayerScreen(
    navController: NavController,
    videoId: String,
    episodeId: String,
    viewModel: PlayerViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val configuration = LocalConfiguration.current
    val isLandscape = configuration.screenWidthDp > configuration.screenHeightDp

    LaunchedEffect(videoId) {
        viewModel.loadVideoDetail(videoId)
    }

    // 如果有指定的episodeId，则播放指定集数
    LaunchedEffect(episodeId, uiState.playListByLine) {
        if (episodeId != "default" && uiState.playListByLine.isNotEmpty()) {
            // 查找包含指定episodeId的线路
            for ((lineId, episodes) in uiState.playListByLine) {
                if (episodes.any { it.id.toString() == episodeId }) {
                    viewModel.playSpecificEpisode(lineId, episodeId)
                    break
                }
            }
        }
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black)
    ) {
        when {
            uiState.isLoading -> {
                LoadingContent()
            }
            
            uiState.error != null -> {
                ErrorContent(
                    error = uiState.error!!,
                    onRetry = { viewModel.loadVideoDetail(videoId) },
                    onBack = { navController.popBackStack() }
                )
            }
            
            uiState.video != null -> {
                if (isLandscape) {
                    LandscapePlayerContent(
                        uiState = uiState,
                        viewModel = viewModel,
                        onBack = { navController.popBackStack() }
                    )
                } else {
                    PortraitPlayerContent(
                        uiState = uiState,
                        viewModel = viewModel,
                        onBack = { navController.popBackStack() }
                    )
                }
            }
        }
        
        // 错误处理
        uiState.error?.let { error ->
            LaunchedEffect(error) {
                viewModel.clearError()
            }
        }
    }
}

@Composable
private fun LandscapePlayerContent(
    uiState: PlayerUiState,
    viewModel: PlayerViewModel,
    onBack: () -> Unit
) {
    Box(modifier = Modifier.fillMaxSize()) {
        // 全屏播放器
        VideoPlayer(
            url = uiState.currentPlayUrl,
            modifier = Modifier.fillMaxSize(),
            onPlaybackStateChanged = { isPlaying, position, duration ->
                viewModel.setIsPlaying(isPlaying)
                viewModel.updatePlaybackPosition(position)
            },
            onError = { error ->
                // 处理播放错误
            }
        )

        // 顶部返回按钮和标题
        TopBar(
            title = uiState.video?.title ?: "",
            subtitle = uiState.selectedEpisodeId?.let { episodeId ->
                val episode = uiState.availableEpisodes.find { it.id.toString() == episodeId }
                "第${episode?.episode ?: episode?.id}集"
            },
            onBack = onBack,
            modifier = Modifier.align(Alignment.TopStart)
        )

        // 底部控制栏（紧凑模式）
        if (uiState.playListByLine.isNotEmpty()) {
            BottomControlBar(
                uiState = uiState,
                viewModel = viewModel,
                modifier = Modifier.align(Alignment.BottomCenter)
            )
        }
    }
}

@Composable
private fun PortraitPlayerContent(
    uiState: PlayerUiState,
    viewModel: PlayerViewModel,
    onBack: () -> Unit
) {
    Column(modifier = Modifier.fillMaxSize()) {
        // 顶部播放器
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .aspectRatio(16f / 9f)
        ) {
            VideoPlayer(
                url = uiState.currentPlayUrl,
                modifier = Modifier.fillMaxSize(),
                onPlaybackStateChanged = { isPlaying, position, duration ->
                    viewModel.setIsPlaying(isPlaying)
                    viewModel.updatePlaybackPosition(position)
                },
                onError = { error ->
                    // 处理播放错误
                }
            )

            // 返回按钮
            IconButton(
                onClick = onBack,
                modifier = Modifier
                    .align(Alignment.TopStart)
                    .padding(8.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.ArrowBack,
                    contentDescription = "返回",
                    tint = Color.White
                )
            }
        }

        // 底部内容
        val listState = rememberLazyListState()
        
        LazyColumn(
            state = listState,
            modifier = Modifier
                .fillMaxSize()
                .background(MaterialTheme.colorScheme.surface)
                .onKeyEvent { keyEvent ->
                    when {
                        keyEvent.key == Key.DirectionUp && keyEvent.type == KeyEventType.KeyDown -> {
                            if (listState.firstVisibleItemIndex == 0 && listState.firstVisibleItemScrollOffset == 0) {
                                // 在顶部，不消费事件，让全局处理器处理
                                false
                            } else {
                                // 不在顶部，让LazyColumn处理滚动
                                false
                            }
                        }
                        else -> false
                    }
                },
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            item {
                // 视频信息
                VideoInfoCard(video = uiState.video!!)
            }

            item {
                // 播放控制
                if (uiState.playListByLine.isNotEmpty()) {
                    PlaybackControlCard(
                        uiState = uiState,
                        viewModel = viewModel
                    )
                }
            }

            item {
                // 播放列表选择
                if (uiState.playListByLine.isNotEmpty()) {
                    CompactPlaylistSelector(
                        availableLines = uiState.availableLines,
                        availableEpisodes = uiState.availableEpisodes,
                        selectedLineId = uiState.selectedLineId,
                        selectedEpisodeId = uiState.selectedEpisodeId,
                        onLineSelected = { viewModel.selectLine(it) },
                        onEpisodeSelected = { viewModel.selectEpisode(it) }
                    )
                }
            }
        }
    }
}

@Composable
private fun TopBar(
    title: String,
    subtitle: String? = null,
    onBack: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(8.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.6f)
        ),
        shape = RoundedCornerShape(8.dp)
    ) {
        Row(
            modifier = Modifier.padding(8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onBack) {
                Icon(
                    imageVector = Icons.Default.ArrowBack,
                    contentDescription = "返回",
                    tint = Color.White
                )
            }
            
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = title,
                    color = Color.White,
                    style = MaterialTheme.typography.titleMedium,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                subtitle?.let {
                    Text(
                        text = it,
                        color = Color.White.copy(alpha = 0.8f),
                        style = MaterialTheme.typography.bodySmall,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }
            }
        }
    }
}

@Composable
private fun BottomControlBar(
    uiState: PlayerUiState,
    viewModel: PlayerViewModel,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(8.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.6f)
        ),
        shape = RoundedCornerShape(8.dp)
    ) {
        Row(
            modifier = Modifier.padding(8.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            // 上一集
            IconButton(
                onClick = { viewModel.playPrevious() },
                enabled = uiState.availableEpisodes.indexOfFirst { 
                    it.id.toString() == uiState.selectedEpisodeId 
                } > 0
            ) {
                Icon(
                    imageVector = Icons.Default.ArrowBack,
                    contentDescription = "上一集",
                    tint = Color.White
                )
            }

            // 当前集数信息
            Text(
                text = uiState.selectedEpisodeId?.let { episodeId ->
                    val episode = uiState.availableEpisodes.find { it.id.toString() == episodeId }
                    "第${episode?.episode ?: episode?.id}集"
                } ?: "",
                color = Color.White,
                style = MaterialTheme.typography.bodyMedium
            )

            // 下一集
            IconButton(
                onClick = { viewModel.playNext() },
                enabled = uiState.availableEpisodes.let { episodes ->
                    val currentIndex = episodes.indexOfFirst { 
                        it.id.toString() == uiState.selectedEpisodeId 
                    }
                    currentIndex >= 0 && currentIndex < episodes.size - 1
                }
            ) {
                Icon(
                    imageVector = Icons.Default.ArrowForward,
                    contentDescription = "下一集",
                    tint = Color.White
                )
            }
        }
    }
}

@Composable
private fun VideoInfoCard(
    video: com.mv.app.data.model.Video
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = video.title ?: "未知标题",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold
            )
            
            Row(
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                video.year?.let {
                    Text(
                        text = "年份: $it",
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
                video.area?.let {
                    Text(
                        text = "地区: $it",
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
            
            video.content?.let {
                Text(
                    text = it,
                    style = MaterialTheme.typography.bodyMedium,
                    maxLines = 3,
                    overflow = TextOverflow.Ellipsis
                )
            }
        }
    }
}

@Composable
private fun PlaybackControlCard(
    uiState: PlayerUiState,
    viewModel: PlayerViewModel
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            // 上一集
            OutlinedButton(
                onClick = { viewModel.playPrevious() },
                enabled = uiState.availableEpisodes.indexOfFirst { 
                    it.id.toString() == uiState.selectedEpisodeId 
                } > 0
            ) {
                Icon(
                    imageVector = Icons.Default.ArrowBack,
                    contentDescription = null,
                    modifier = Modifier.size(18.dp)
                )
                Spacer(modifier = Modifier.width(4.dp))
                Text("上一集")
            }

            // 下一集
            OutlinedButton(
                onClick = { viewModel.playNext() },
                enabled = uiState.availableEpisodes.let { episodes ->
                    val currentIndex = episodes.indexOfFirst { 
                        it.id.toString() == uiState.selectedEpisodeId 
                    }
                    currentIndex >= 0 && currentIndex < episodes.size - 1
                }
            ) {
                Icon(
                    imageVector = Icons.Default.ArrowForward,
                    contentDescription = null,
                    modifier = Modifier.size(18.dp)
                )
                Spacer(modifier = Modifier.width(4.dp))
                Text("下一集")
            }
        }
    }
}

@Composable
private fun LoadingContent() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            CircularProgressIndicator(color = Color.White)
            Text(
                text = "加载中...",
                color = Color.White,
                style = MaterialTheme.typography.bodyLarge
            )
        }
    }
}

@Composable
private fun ErrorContent(
    error: String,
    onRetry: () -> Unit,
    onBack: () -> Unit
) {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Text(
                text = "播放失败",
                color = Color.White,
                style = MaterialTheme.typography.titleLarge
            )
            Text(
                text = error,
                color = Color.White.copy(alpha = 0.8f),
                style = MaterialTheme.typography.bodyMedium
            )
            Row(
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                OutlinedButton(
                    onClick = onBack,
                    colors = ButtonDefaults.outlinedButtonColors(
                        contentColor = Color.White
                    )
                ) {
                    Text("返回")
                }
                Button(
                    onClick = onRetry,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.primary
                    )
                ) {
                    Text("重试")
                }
            }
        }
    }
} 