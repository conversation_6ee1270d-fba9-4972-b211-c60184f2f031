package com.mv.app.ui.player

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.mv.app.data.model.Video
import com.mv.app.data.model.PlayListItem
import com.mv.app.data.repository.VideoRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class PlayerViewModel @Inject constructor(
    private val repository: VideoRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(PlayerUiState())
    val uiState: StateFlow<PlayerUiState> = _uiState.asStateFlow()

    fun loadVideoDetail(videoId: String) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(
                isLoading = true,
                error = null
            )

            try {
                val result = repository.getVideoDetail(videoId.toInt())
                result.fold(
                    onSuccess = { video ->
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            video = video,
                            error = null,
                            playListByLine = video.playListByLine ?: emptyMap(),
                            availableLines = video.playListByLine?.keys?.toList() ?: emptyList()
                        )
                        
                        // 设置默认播放线路和集数
                        val defaultLine = _uiState.value.availableLines.firstOrNull()
                        if (defaultLine != null) {
                            selectLine(defaultLine)
                        }
                    },
                    onFailure = { exception ->
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            error = exception.message ?: "加载视频详情失败"
                        )
                    }
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message ?: "加载视频详情失败"
                )
            }
        }
    }

    fun selectLine(lineId: String) {
        val currentState = _uiState.value
        val episodes = currentState.playListByLine[lineId] ?: emptyList()
        
        _uiState.value = currentState.copy(
            selectedLineId = lineId,
            availableEpisodes = episodes,
            selectedEpisodeId = episodes.firstOrNull()?.id?.toString()
        )
        
        // 自动选择第一集
        episodes.firstOrNull()?.let { episode ->
            selectEpisode(episode.id.toString())
        }
    }

    fun selectEpisode(episodeId: String) {
        val currentState = _uiState.value
        val episode = currentState.availableEpisodes.find { it.id.toString() == episodeId }
        
        _uiState.value = currentState.copy(
            selectedEpisodeId = episodeId,
            currentPlayUrl = episode?.url
        )
    }

    fun playSpecificEpisode(lineId: String, episodeId: String) {
        selectLine(lineId)
        selectEpisode(episodeId)
    }

    fun playNext() {
        val currentState = _uiState.value
        val currentEpisodeIndex = currentState.availableEpisodes.indexOfFirst { 
            it.id.toString() == currentState.selectedEpisodeId 
        }
        
        if (currentEpisodeIndex >= 0 && currentEpisodeIndex < currentState.availableEpisodes.size - 1) {
            val nextEpisode = currentState.availableEpisodes[currentEpisodeIndex + 1]
            selectEpisode(nextEpisode.id.toString())
        }
    }

    fun playPrevious() {
        val currentState = _uiState.value
        val currentEpisodeIndex = currentState.availableEpisodes.indexOfFirst { 
            it.id.toString() == currentState.selectedEpisodeId 
        }
        
        if (currentEpisodeIndex > 0) {
            val prevEpisode = currentState.availableEpisodes[currentEpisodeIndex - 1]
            selectEpisode(prevEpisode.id.toString())
        }
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }

    fun updatePlaybackPosition(position: Long) {
        _uiState.value = _uiState.value.copy(playbackPosition = position)
    }

    fun setIsPlaying(isPlaying: Boolean) {
        _uiState.value = _uiState.value.copy(isPlaying = isPlaying)
    }
}

data class PlayerUiState(
    val isLoading: Boolean = false,
    val video: Video? = null,
    val error: String? = null,
    
    // 播放列表相关
    val playListByLine: Map<String, List<PlayListItem>> = emptyMap(),
    val availableLines: List<String> = emptyList(),
    val selectedLineId: String? = null,
    val availableEpisodes: List<PlayListItem> = emptyList(),
    val selectedEpisodeId: String? = null,
    val currentPlayUrl: String? = null,
    
    // 播放器状态
    val isPlaying: Boolean = false,
    val playbackPosition: Long = 0L,
    val duration: Long = 0L
) 