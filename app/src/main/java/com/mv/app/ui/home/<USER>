package com.mv.app.ui.home

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.key.*
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.mv.app.data.model.BannerItem
import com.mv.app.data.model.Video
import com.mv.app.ui.components.TVHeroBanner
import com.mv.app.ui.components.TVVideoRow

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HomeScreen(
    onVideoClick: (Video) -> Unit,
    onBannerClick: (BannerItem) -> Unit,
    onNavigateToCategory: (Int) -> Unit,
    onNavigateToSidebar: (() -> Unit)? = null, // 新增：导航到侧边栏的回调
    modifier: Modifier = Modifier,
    viewModel: HomeViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    Column(modifier = modifier.fillMaxSize()) {
        when {
            uiState.isLoading -> {
                LoadingContent()
            }
            uiState.error != null -> {
                ErrorContent(
                    error = uiState.error!!,
                    onRetry = { viewModel.refresh() }
                )
            }
            else -> {
                SuccessContent(
                    uiState = uiState,
                    onVideoClick = onVideoClick,
                    onBannerClick = onBannerClick,
                    onNavigateToCategory = onNavigateToCategory,
                    onNavigateToSidebar = onNavigateToSidebar
                )
            }
        }
    }
}

@Composable
private fun LoadingContent() {
    val listState = rememberLazyListState()
    
    LazyColumn(
        state = listState,
        modifier = Modifier
            .fillMaxSize()
            .onKeyEvent { keyEvent ->
                when {
                    keyEvent.key == Key.DirectionUp && keyEvent.type == KeyEventType.KeyDown -> {
                        if (listState.firstVisibleItemIndex == 0 && listState.firstVisibleItemScrollOffset == 0) {
                            false // 让全局处理器处理
                        } else {
                            false // 让LazyColumn处理滚动
                        }
                    }
                    else -> false
                }
            },
        verticalArrangement = Arrangement.spacedBy(32.dp),
        contentPadding = PaddingValues(vertical = 24.dp)
    ) {
        // 主横幅骨架
        item {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(540.dp)
                    .padding(horizontal = 48.dp)
            ) {
                Card(
                    modifier = Modifier.fillMaxSize(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surfaceVariant
                    )
                ) {}
            }
        }
        
        // 内容行骨架
        items(6) { index ->
            val titles = listOf("热门推荐", "最新电影", "最新剧集", "最新动漫", "最新综艺", "最新纪录片")
            TVVideoRow(
                title = titles[index],
                videos = emptyList(),
                onVideoClick = {},
                onMoreClick = null,
                onNavigateToSidebar = null
            )
        }
    }
}

@Composable
private fun ErrorContent(
    error: String,
    onRetry: () -> Unit
) {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Text(
                text = error,
                style = MaterialTheme.typography.headlineSmall,
                color = MaterialTheme.colorScheme.error
            )
            
            Button(
                onClick = onRetry,
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.primary
                )
            ) {
                Text("重试")
            }
        }
    }
}

@Composable
private fun SuccessContent(
    uiState: HomeUiState,
    onVideoClick: (Video) -> Unit,
    onBannerClick: (BannerItem) -> Unit,
    onNavigateToCategory: (Int) -> Unit,
    onNavigateToSidebar: (() -> Unit)? = null
) {
    val listState = rememberLazyListState()
    
    LazyColumn(
        state = listState,
        modifier = Modifier
            .fillMaxSize()
            .onKeyEvent { keyEvent ->
                when {
                    // 当用户在内容区域按上方向键时，如果已经在顶部，则不消费事件，让它传递给全局处理器
                    keyEvent.key == Key.DirectionUp && keyEvent.type == KeyEventType.KeyDown -> {
                        if (listState.firstVisibleItemIndex == 0 && listState.firstVisibleItemScrollOffset == 0) {
                            // 已经在顶部，不消费事件，让全局处理器处理
                            false
                        } else {
                            // 不在顶部，让LazyColumn处理滚动
                            false
                        }
                    }
                    else -> false
                }
            },
        verticalArrangement = Arrangement.spacedBy(32.dp),
        contentPadding = PaddingValues(vertical = 24.dp)
    ) {
        // 主横幅 - 使用热门推荐内容
        item {
            val heroVideos = uiState.bannerItems
            
            if (heroVideos.isNotEmpty()) {
                TVHeroBanner(
                    bannerVideos = heroVideos,
                    onVideoClick = onBannerClick
                )
            }
        }

        // 热门推荐
//        if (uiState.movieVideos.isNotEmpty()) {
//            item {
//                TVVideoRow(
//                    title = "热门推荐",
//                    videos = uiState.movieVideos.take(8),
//                    onVideoClick = onVideoClick,
//                    onMoreClick = { onNavigateToCategory(1) },
//                    onNavigateToSidebar = onNavigateToSidebar
//                )
//            }
//        }

        // 最新电影
        if (uiState.movieVideos.isNotEmpty()) {
            item {
                TVVideoRow(
                    title = "最新电影",
                    videos = uiState.movieVideos,
                    onVideoClick = onVideoClick,
                    onMoreClick = { onNavigateToCategory(1) },
                    onNavigateToSidebar = onNavigateToSidebar
                )
            }
        }

        // 最新剧集
        if (uiState.tvVideos.isNotEmpty()) {
            item {
                TVVideoRow(
                    title = "最新剧集",
                    videos = uiState.tvVideos,
                    onVideoClick = onVideoClick,
                    onMoreClick = { onNavigateToCategory(2) },
                    onNavigateToSidebar = onNavigateToSidebar
                )
            }
        }

        // 最新动漫
        if (uiState.animeVideos.isNotEmpty()) {
            item {
                TVVideoRow(
                    title = "最新动漫",
                    videos = uiState.animeVideos,
                    onVideoClick = onVideoClick,
                    onMoreClick = { onNavigateToCategory(3) },
                    onNavigateToSidebar = onNavigateToSidebar
                )
            }
        }

        // 最新综艺
        if (uiState.varietyVideos.isNotEmpty()) {
            item {
                TVVideoRow(
                    title = "最新综艺",
                    videos = uiState.varietyVideos,
                    onVideoClick = onVideoClick,
                    onMoreClick = { onNavigateToCategory(4) },
                    onNavigateToSidebar = onNavigateToSidebar
                )
            }
        }

        // 最新纪录片
        if (uiState.documentaryVideos.isNotEmpty()) {
            item {
                TVVideoRow(
                    title = "最新纪录片",
                    videos = uiState.documentaryVideos,
                    onVideoClick = onVideoClick,
                    onMoreClick = { onNavigateToCategory(5) },
                    onNavigateToSidebar = onNavigateToSidebar
                )
            }
        }

        // 最新短剧
        if (uiState.shortVideos.isNotEmpty()) {
            item {
                TVVideoRow(
                    title = "最新短剧",
                    videos = uiState.shortVideos,
                    onVideoClick = onVideoClick,
                    onMoreClick = { onNavigateToCategory(7) },
                    onNavigateToSidebar = onNavigateToSidebar
                )
            }
        }
    }
} 