package com.mv.app.ui.search

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.mv.app.data.model.Video
import com.mv.app.data.repository.VideoRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

@OptIn(FlowPreview::class)
@HiltViewModel
class SearchViewModel @Inject constructor(
    private val repository: VideoRepository
) : ViewModel() {

    private val _searchQuery = MutableStateFlow("")
    val searchQuery: StateFlow<String> = _searchQuery.asStateFlow()

    private val _uiState = MutableStateFlow(SearchUiState())
    val uiState: StateFlow<SearchUiState> = _uiState.asStateFlow()

    private val _searchHistory = MutableStateFlow<List<String>>(emptyList())
    val searchHistory: StateFlow<List<String>> = _searchHistory.asStateFlow()

    init {
        // 防抖搜索：当搜索查询改变时，延迟300ms后执行搜索
        searchQuery
            .debounce(300)
            .distinctUntilChanged()
            .onEach { query ->
                if (query.isNotBlank()) {
                    searchVideos(query)
                } else {
                    _uiState.value = SearchUiState()
                }
            }
            .launchIn(viewModelScope)
    }

    fun updateSearchQuery(query: String) {
        _searchQuery.value = query
    }

    fun searchVideos(query: String, loadMore: Boolean = false) {
        if (query.isBlank()) return

        viewModelScope.launch {
            if (!loadMore) {
                _uiState.value = _uiState.value.copy(
                    isLoading = true,
                    error = null
                )
            } else {
                _uiState.value = _uiState.value.copy(isLoadingMore = true)
            }

            try {
                val currentPage = if (loadMore) _uiState.value.currentPage + 1 else 1
                val result = repository.searchVideos(
                    query = query,
                    page = currentPage,
                    pageSize = 20
                )

                result.fold(
                    onSuccess = { videos ->
                        val currentVideos = if (loadMore) _uiState.value.searchResults else emptyList()
                        val newVideos = currentVideos + videos
                        
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            isLoadingMore = false,
                            searchResults = newVideos,
                            currentPage = currentPage,
                            hasMoreData = videos.size == 20, // 如果返回的数据等于页面大小，说明可能还有更多数据
                            error = null
                        )

                        // 添加到搜索历史
                        if (!loadMore) {
                            addToSearchHistory(query)
                        }
                    },
                    onFailure = { exception ->
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            isLoadingMore = false,
                            error = exception.message ?: "搜索失败"
                        )
                    }
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    isLoadingMore = false,
                    error = e.message ?: "搜索失败"
                )
            }
        }
    }

    fun loadMoreResults() {
        val currentQuery = searchQuery.value
        if (currentQuery.isNotBlank() && _uiState.value.hasMoreData && !_uiState.value.isLoadingMore) {
            searchVideos(currentQuery, loadMore = true)
        }
    }

    private fun addToSearchHistory(query: String) {
        val currentHistory = _searchHistory.value.toMutableList()
        currentHistory.remove(query) // 移除已存在的相同查询
        currentHistory.add(0, query) // 添加到开头
        if (currentHistory.size > 10) { // 限制历史记录数量
            currentHistory.removeAt(currentHistory.size - 1)
        }
        _searchHistory.value = currentHistory
    }

    fun removeFromSearchHistory(query: String) {
        val currentHistory = _searchHistory.value.toMutableList()
        currentHistory.remove(query)
        _searchHistory.value = currentHistory
    }

    fun clearSearchHistory() {
        _searchHistory.value = emptyList()
    }

    fun clearSearchResults() {
        _uiState.value = SearchUiState()
        _searchQuery.value = ""
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
}

data class SearchUiState(
    val isLoading: Boolean = false,
    val isLoadingMore: Boolean = false,
    val searchResults: List<Video> = emptyList(),
    val currentPage: Int = 0,
    val hasMoreData: Boolean = true,
    val error: String? = null
) 