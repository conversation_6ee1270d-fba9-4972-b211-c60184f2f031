package com.mv.app.ui.navigation

import androidx.navigation.NamedNavArgument
import androidx.navigation.NavType
import androidx.navigation.navArgument

sealed class MvDestination(
    val route: String,
    val arguments: List<NamedNavArgument> = emptyList()
) {
    // 首页
    object Home : MvDestination("home")
    
    // 分类页 - 添加分类类型参数
    object Category : MvDestination(
        route = "category/{categoryType}",
        arguments = listOf(
            navArgument("categoryType") { type = NavType.StringType }
        )
    ) {
        fun createRoute(categoryType: String) = "category/$categoryType"
    }
    
    // 搜索页
    object Search : MvDestination("search")
    
    // 我的页面
    object Profile : MvDestination("profile")
    
    // 详情页
    object Detail : MvDestination(
        route = "detail/{videoId}",
        arguments = listOf(
            navArgument("videoId") { type = NavType.StringType }
        )
    ) {
        fun createRoute(videoId: String) = "detail/$videoId"
    }
    
    // 播放页
    object Player : MvDestination(
        route = "player/{videoId}/{episodeId}",
        arguments = listOf(
            navArgument("videoId") { type = NavType.StringType },
            navArgument("episodeId") { type = NavType.StringType }
        )
    ) {
        fun createRoute(videoId: String, episodeId: String) = "player/$videoId/$episodeId"
    }
} 