package com.mv.app.di

import android.content.Context
import androidx.room.Room
import com.mv.app.data.database.MvDatabase
import com.mv.app.data.database.dao.VideoDao
import com.mv.app.data.database.dao.UserDao
import com.mv.app.data.database.dao.WatchHistoryDao
import com.mv.app.data.database.dao.FavoriteDao
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {
    
    @Provides
    @Singleton
    fun provideDatabase(@ApplicationContext context: Context): MvDatabase {
        return Room.databaseBuilder(
            context,
            MvDatabase::class.java,
            "mv_database"
        ).build()
    }
    
    @Provides
    fun provideVideoDao(database: MvDatabase): VideoDao {
        return database.videoDao()
    }
    
    @Provides
    fun provideUserDao(database: MvDatabase): UserDao {
        return database.userDao()
    }
    
    @Provides
    fun provideWatchHistoryDao(database: MvDatabase): WatchHistoryDao {
        return database.watchHistoryDao()
    }
    
    @Provides
    fun provideFavoriteDao(database: MvDatabase): FavoriteDao {
        return database.favoriteDao()
    }
} 