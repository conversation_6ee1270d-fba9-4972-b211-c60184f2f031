package com.mv.app.data.database.entity

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "videos")
data class VideoEntity(
    @PrimaryKey
    val id: String,
    val title: String,
    val description: String,
    val posterUrl: String,
    val backdropUrl: String,
    val year: Int,
    val rating: Double,
    val duration: Int,
    val category: String,
    val genres: String, // JSON string
    val countries: String, // JSON string
    val directors: String, // JSON string
    val actors: String, // JSON string
    val createdAt: Long,
    val updatedAt: Long
) 