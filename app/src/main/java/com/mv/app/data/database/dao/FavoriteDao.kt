package com.mv.app.data.database.dao

import androidx.room.*
import com.mv.app.data.database.entity.FavoriteEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface FavoriteDao {
    
    @Query("SELECT * FROM favorites ORDER BY createdAt DESC")
    fun getAllFavorites(): Flow<List<FavoriteEntity>>
    
    @Query("SELECT * FROM favorites WHERE userId = :userId ORDER BY createdAt DESC")
    fun getFavoritesByUser(userId: String): Flow<List<FavoriteEntity>>
    
    @Query("SELECT * FROM favorites WHERE videoId = :videoId AND userId = :userId")
    fun getFavorite(videoId: String, userId: String): FavoriteEntity?
    
    @Query("SELECT EXISTS(SELECT 1 FROM favorites WHERE videoId = :videoId AND userId = :userId)")
    fun isFavorite(videoId: String, userId: String): <PERSON><PERSON><PERSON>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertFavorite(favorite: FavoriteEntity)
    
    @Delete
    fun deleteFavorite(favorite: FavoriteEntity)
    
    @Query("DELETE FROM favorites WHERE videoId = :videoId AND userId = :userId")
    fun deleteFavoriteByVideoAndUser(videoId: String, userId: String)
    
    @Query("DELETE FROM favorites WHERE userId = :userId")
    fun deleteFavoritesByUser(userId: String)
    
    @Query("DELETE FROM favorites")
    fun deleteAllFavorites()
} 