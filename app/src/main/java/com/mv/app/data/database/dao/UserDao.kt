package com.mv.app.data.database.dao

import androidx.room.*
import com.mv.app.data.database.entity.UserEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface UserDao {
    
    @Query("SELECT * FROM users WHERE id = :userId")
    fun getUserById(userId: String): UserEntity?
    
    @Query("SELECT * FROM users LIMIT 1")
    fun getCurrentUser(): Flow<UserEntity?>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertUser(user: UserEntity)
    
    @Update
    fun updateUser(user: UserEntity)
    
    @Delete
    fun deleteUser(user: UserEntity)
    
    @Query("DELETE FROM users")
    fun deleteAllUsers()
} 