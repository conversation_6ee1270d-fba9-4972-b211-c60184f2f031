# 侧边栏焦点定位修复说明

## 问题描述

用户反馈：当从主内容区域按遥控器左键返回到侧边栏时，焦点没有定位到当前页面对应的侧边栏选项上，而是定位到了侧边栏的第一个或随机的选项。

## 问题分析

### 根本原因
1. **焦点请求器作用域问题** - `sideNavigationFocusRequester` 是整个侧边栏的焦点请求器，而不是特定选项的
2. **状态同步问题** - 当用户从主内容区域返回时，`selectedTabIndex` 可能与当前实际页面不匹配
3. **焦点传递机制** - 侧边栏接收到焦点后没有自动将焦点转移到当前选中的项

## 修复方案

### 1. 增强侧边栏焦点管理
**文件**: `app/src/main/java/com/mv/app/ui/components/TVSideNavigation.kt`

添加了 `onFocusChanged` 监听器，当整个侧边栏获得焦点时，自动将焦点转移到当前选中的项：

```kotlin
Surface(
    modifier = modifier
        .onFocusChanged { focusState ->
            // 当整个侧边栏获得焦点时，将焦点转移到当前选中的项
            if (focusState.isFocused && selectedTabIndex in focusRequesters.indices) {
                focusRequesters[selectedTabIndex].requestFocus()
            }
        }
    // ...
)
```

### 2. 智能路由检测和状态同步
**文件**: `app/src/main/java/com/mv/app/MainActivity.kt`

改进了 `onNavigateToSidebar` 回调，添加了路由检测逻辑，确保 `selectedTabIndex` 与当前页面匹配：

```kotlin
onNavigateToSidebar = {
    val currentRoute = navController.currentDestination?.route ?: ""
    
    // 根据当前路由更新selectedTabIndex
    when {
        currentRoute == "search" -> selectedTabIndex = 0
        currentRoute == "home" -> selectedTabIndex = 1
        currentRoute.startsWith("category/movies") -> selectedTabIndex = 2
        currentRoute.startsWith("category/tv") -> selectedTabIndex = 3
        currentRoute.startsWith("category/anime") -> selectedTabIndex = 4
        currentRoute.startsWith("category/variety") -> selectedTabIndex = 5
        currentRoute.startsWith("category/short") -> selectedTabIndex = 6
        currentRoute.startsWith("category/documentary") -> selectedTabIndex = 7
        currentRoute == "profile" -> selectedTabIndex = 8
    }
    
    sideNavigationFocusRequester.requestFocus()
}
```

### 3. 改进焦点同步机制
**文件**: `app/src/main/java/com/mv/app/ui/components/TVSideNavigation.kt`

确保 `LaunchedEffect(selectedTabIndex)` 能够正确响应状态变化：

```kotlin
LaunchedEffect(selectedTabIndex) {
    if (selectedTabIndex in focusRequesters.indices) {
        focusRequesters[selectedTabIndex].requestFocus()
        currentFocusIndex = selectedTabIndex
    }
}
```

## 工作流程

### 用户操作流程
1. **用户在主内容区域** - 浏览视频、搜索等
2. **按左键返回侧边栏** - 触发 `onNavigateToSidebar` 回调
3. **路由检测** - 系统检测当前页面路由
4. **状态同步** - 更新 `selectedTabIndex` 匹配当前页面
5. **焦点请求** - 请求侧边栏焦点
6. **自动定位** - 侧边栏自动将焦点转移到正确的选项

### 技术实现流程
1. `onNavigateToSidebar` 被触发
2. 检测 `navController.currentDestination?.route`
3. 根据路由更新 `selectedTabIndex`
4. 调用 `sideNavigationFocusRequester.requestFocus()`
5. 侧边栏的 `onFocusChanged` 被触发
6. 调用 `focusRequesters[selectedTabIndex].requestFocus()`
7. 焦点正确定位到当前页面对应的侧边栏选项

## 支持的路由映射

| 路由模式 | 侧边栏索引 | 显示名称 |
|----------|-----------|----------|
| `search` | 0 | 搜索 |
| `home` | 1 | 首页 |
| `category/movies` | 2 | 电影 |
| `category/tv` | 3 | 电视剧 |
| `category/anime` | 4 | 动漫 |
| `category/variety` | 5 | 综艺 |
| `category/short` | 6 | 短剧 |
| `category/documentary` | 7 | 纪录片 |
| `profile` | 8 | 我的 |

## 调试信息

添加了详细的日志输出，便于调试：
- 当前路由检测日志
- 焦点请求日志
- 状态同步日志

日志标签：`MainActivity`

## 测试方法

### 基本功能测试
1. 启动应用，导航到任意分类页面（如电影）
2. 使用右键进入主内容区域
3. 按左键返回侧边栏
4. 验证焦点是否正确定位到"电影"选项

### 全面测试
对每个页面重复上述测试：
- 搜索页面
- 首页
- 各个分类页面（电影、电视剧、动漫、综艺、短剧、纪录片）
- 个人页面

### 预期结果
- 焦点应该始终定位到当前页面对应的侧边栏选项
- 不应该出现焦点定位到错误选项的情况
- 焦点切换应该流畅，没有明显延迟

## 编译状态

✅ **编译成功** - 所有修改已通过编译测试
✅ **无语法错误** - 代码语法正确
✅ **依赖完整** - 所有必要的导入都已包含

## 后续优化建议

1. **性能优化** - 考虑缓存路由映射关系
2. **动画效果** - 为焦点切换添加平滑动画
3. **错误处理** - 添加未知路由的处理逻辑
4. **用户体验** - 考虑添加视觉反馈指示当前位置

现在侧边栏焦点定位问题应该已经解决，用户从主内容区域按左键返回时，焦点会正确定位到当前页面对应的侧边栏选项。
