# Android应用API集成总结

## 概述

本文档总结了将Android应用与实际的mv-api后端集成的所有修改。我们成功地将应用从使用模拟数据改为使用真实的API数据。

## 主要修改内容

### 1. 数据模型更新 (Video.kt)

**修改文件**: `mv-app/app/src/main/java/com/mv/app/data/model/Video.kt`

**主要变更**:
- 将Video模型从复杂的嵌套结构简化为与API响应匹配的扁平结构
- 字段类型从String改为Int (id字段)
- 添加了API响应包装类：VideoListResponse, VideoDetailResponse, BannerResponse等
- 新增了PlayListItem, VodUrl, DownloadItem等相关数据类
- 添加了Pagination数据类支持分页

**关键字段**:
```kotlin
data class Video(
    val id: Int,                    // 从String改为Int
    val title: String?,             // 可空字段
    val pic: String?,              // 图片URL
    val year: Int?,                // 年份
    val content: String?,          // 剧情简介
    val director: String?,         // 导演
    val starring: String?,         // 主演
    val area: String?,             // 地区
    val typeId: Int?,              // 分类ID (1:电影, 2:剧集, 3:动漫, 4:综艺, 5:纪录片, 7:短剧)
    val remark: String?,           // 状态备注
    // ... 其他字段
)
```

### 2. API服务接口重构 (MvApiService.kt)

**修改文件**: `mv-app/app/src/main/java/com/mv/app/data/api/MvApiService.kt`

**API端点对应**:
- `GET /videos` - 获取视频列表(支持分类、分页、筛选)
- `GET /videos/{id}` - 获取视频详情
- `GET /videos/search` - 搜索视频
- `GET /videos/getBannerVideos` - 获取轮播图数据

**参数支持**:
- 分页: page, pageSize
- 分类过滤: typeId, area, year, director, starring, language
- 搜索: q (关键词)

### 3. Repository层重构 (VideoRepository.kt)

**修改文件**: `mv-app/app/src/main/java/com/mv/app/data/repository/VideoRepository.kt`

**主要功能**:
- `getHomeVideos()`: 获取首页数据(轮播图+各分类视频)
- `getCategoryVideos()`: 获取指定分类视频列表
- `searchVideos()`: 搜索视频
- `getVideoDetail()`: 获取视频详情
- `getCategories()`: 获取分类列表

**错误处理**:
- 所有方法返回Result<T>类型，支持成功/失败处理
- 统一的错误信息处理

### 4. ViewModel层适配

#### HomeViewModel.kt
- 适配新的Repository接口
- 支持轮播图数据(BannerItem)
- 按分类组织首页内容
- 完善的加载状态和错误处理

#### SearchViewModel.kt
- 实现防抖搜索(300ms延迟)
- 支持分页加载更多
- 搜索历史管理
- 优化的状态管理

#### CategoryViewModel.kt
- 支持动态分类列表
- 分类切换和筛选
- 分页加载支持

#### DetailViewModel.kt
- videoId参数从String改为Int
- 适配新的数据结构

### 5. UI组件更新

#### VideoCard.kt
- 适配新的Video数据结构
- 处理可空字段显示
- 优化图片URL处理逻辑
- 支持普通和大尺寸两种样式

#### VideoRow.kt
- 新增BannerRow组件支持轮播图
- 优化加载状态(Shimmer效果)
- 改进空状态处理
- 统一的图片URL处理

#### HomeScreen.kt
- 完全重构页面布局
- 支持轮播图展示
- 按分类组织内容展示
- 改进的加载和错误状态

### 6. 网络配置更新

#### NetworkModule.kt
**修改文件**: `mv-app/app/src/main/java/com/mv/app/di/NetworkModule.kt`

**重要配置**:
```kotlin
.baseUrl("https://mv-api.your-username.workers.dev/")
```

**注意**: 需要用户手动替换为实际的API部署地址

### 7. 新增文档

#### API_CONFIG.md
- API部署和配置指南
- 环境配置说明
- 常见问题解决方案

## 数据库字段映射

| API字段 | Android字段 | 说明 |
|---------|-------------|------|
| id | id | 视频ID(Int) |
| title | title | 视频标题 |
| pic/savePath | pic | 封面图片URL |
| year | year | 年份 |
| content | content | 剧情简介 |
| director | director | 导演 |
| starring | starring | 主演 |
| area | area | 地区 |
| type_id | typeId | 分类ID |
| remark | remark | 状态备注 |
| backdrop_path | backdropPath | 背景图路径 |

## 分类映射

| typeId | 分类名称 |
|--------|----------|
| 1 | 电影 |
| 2 | 剧集 |
| 3 | 动漫 |
| 4 | 综艺 |
| 5 | 纪录片 |
| 7 | 短剧 |

## 图片URL处理

应用中统一使用以下逻辑处理图片URL:

```kotlin
private fun getImageUrl(pic: String?): String {
    return when {
        pic.isNullOrBlank() -> "https://img.jukuku.top/default.jpg"
        pic.startsWith("http://") || pic.startsWith("https://") -> pic
        else -> "https://img.jukuku.top/$pic"
    }
}
```

## 部署步骤

### 1. 部署API
```bash
cd mv-api
npm install
npm run deploy
```

### 2. 配置Android应用
1. 记录API部署地址
2. 修改`NetworkModule.kt`中的baseUrl
3. 编译运行Android应用

### 3. 测试验证
- 首页数据加载
- 分类筛选功能
- 搜索功能
- 视频详情页面

## 技术特性

### 响应式设计
- StateFlow + Compose的响应式UI
- 自动状态更新和重组

### 性能优化
- 图片懒加载(Coil)
- 分页加载避免内存溢出
- 防抖搜索减少API调用

### 错误处理
- 网络错误重试机制
- 用户友好的错误提示
- 优雅的降级处理

### 用户体验
- Shimmer加载效果
- 流畅的页面导航
- 响应式布局适配

## 后续优化建议

1. **缓存机制**: 添加本地缓存减少网络请求
2. **离线支持**: 实现基本的离线功能
3. **性能监控**: 添加网络请求性能监控
4. **用户偏好**: 保存用户浏览历史和偏好设置
5. **推送通知**: 集成推送服务通知新内容

## 故障排除

### 常见问题
1. **网络连接失败**: 检查API地址配置
2. **数据为空**: 确认数据库有测试数据
3. **图片不显示**: 检查图片URL和网络权限
4. **搜索无结果**: 确认搜索接口正常工作

### 调试建议
1. 启用网络日志查看API请求
2. 检查Retrofit日志确认请求格式
3. 使用浏览器测试API端点
4. 检查Logcat中的错误信息

## 总结

通过本次API集成，Android应用成功从使用模拟数据转换为使用真实的后端API。主要改进包括:

- ✅ 完整的数据模型重构
- ✅ 真实API接口集成  
- ✅ 响应式UI状态管理
- ✅ 完善的错误处理机制
- ✅ 优化的用户体验
- ✅ 模块化的代码架构

应用现在可以显示真实的视频数据，支持分类浏览、搜索、详情查看等完整功能，为后续的播放器集成和高级功能开发奠定了坚实基础。 